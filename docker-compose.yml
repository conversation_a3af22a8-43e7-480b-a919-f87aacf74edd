services:
  web:
    build: 
      context: .
      target: production
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE
    command: >
      sh -c "cd heibooky && 
      python manage.py migrate &&
      daphne -b 0.0.0.0 -p 8000 heibooky.asgi:application"
    volumes:
      - log_volume:/var/log/heibooky
      - static_volume:/app/heibooky/staticfiles 
      - media_volume:/app/heibooky/media        
    expose:
      - 8000
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - backend
      - monitoring
    environment:
      - REDIS_PASSWORD_FILE=/run/secrets/redis_password
      - REDIS_USERNAME=django_app
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
    secrets:
      - redis_password
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  celery-worker:
    build: 
      context: .
      target: production
    command: >
      sh -c "cd heibooky &&
      celery -A heibooky worker 
      --loglevel=info
      --pool=solo
      --max-tasks-per-child=50"
    volumes:
      - log_volume:/var/log/heibooky
      - /var/run/celery:/var/run/celery
    user: "1000:1000" 
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - backend
      - monitoring
    environment:
      - REDIS_PASSWORD_FILE=/run/secrets/redis_password
      - REDIS_USERNAME=celery_worker
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
    secrets:
      - redis_password
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  celery-beat:
    build: 
      context: .
      target: production
    command: >
      sh -c "cd heibooky &&
      celery -A heibooky beat 
      --loglevel=info 
      --scheduler django_celery_beat.schedulers:DatabaseScheduler"
    volumes:
      - log_volume:/var/log/heibooky
      - /var/run/celery:/var/run/celery
    user: "1000:1000"
    depends_on:
      redis:
        condition: service_healthy
      celery-worker:
        condition: service_started
      web:
        condition: service_started
    networks:
      - backend
      - monitoring
    environment:
      - REDIS_PASSWORD_FILE=/run/secrets/redis_password
      - REDIS_USERNAME=celery_beat
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
    secrets:
      - redis_password
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  redis:
    image: redis:7-alpine
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - SETGID
      - SETUID
      - DAC_OVERRIDE
    user: "999:999"  # redis user in alpine
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/etc/redis/redis.conf:ro
      - ./redis/users.acl:/etc/redis/users.acl:ro
    sysctls:
      - net.core.somaxconn=511
    command: >
      sh -c 'redis-server /etc/redis/redis.conf \
        --aclfile /etc/redis/users.acl'
    secrets:
      - redis_password
    mem_limit: 512m
    cpus: 0.5
    networks:
      - backend
      - monitoring
    healthcheck:
      test: ["CMD-SHELL", "redis-cli --no-auth-warning --user monitoring -a $(cat /run/secrets/redis_password) ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

  nginx:
    image: nginx:1.25-alpine
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE
      - CHOWN
      - SETGID
      - SETUID
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/logs:/var/log/nginx
      - static_volume:/app/heibooky/staticfiles:ro
      - media_volume:/app/heibooky/media:ro
      - ./certbot/conf:/etc/letsencrypt:ro
      - ./certbot/www:/var/www/certbot:ro
      - nginx_cache:/var/cache/nginx
      - nginx_run:/var/run
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - web
    networks:
      - backend
      - monitoring
    restart: unless-stopped

  certbot:
    image: certbot/certbot
    volumes:
      - ./certbot/conf:/etc/letsencrypt
      - ./certbot/www:/var/www/certbot
    command: certonly --webroot --webroot-path=/var/www/certbot --email <EMAIL> --agree-tos --no-eff-email -d backend.heibooky.com
    depends_on:
      - nginx

  # Monitoring Stack
  prometheus:
    image: prom/prometheus:v2.47.0
    container_name: prometheus
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETGID
      - SETUID
    user: "nobody:nobody"
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./monitoring/prometheus/alert_rules.yml:/etc/prometheus/alert_rules.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=15d'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    ports:
      - "9090:9090"
    networks:
      - monitoring
    restart: unless-stopped
    mem_limit: 1g
    cpus: 0.5

  grafana:
    image: grafana/grafana:10.1.0
    container_name: grafana
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETGID
      - SETUID
    user: "472:472"  # grafana user
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/grafana.ini:/etc/grafana/grafana.ini:ro
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD:-admin123}
      - GF_SECURITY_SECRET_KEY=${GRAFANA_SECRET_KEY:-your-secret-key}
    ports:
      - "3000:3000"
    networks:
      - monitoring
    restart: unless-stopped
    mem_limit: 512m
    cpus: 0.5

  alertmanager:
    image: prom/alertmanager:v0.26.0
    container_name: alertmanager
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETGID
      - SETUID
    user: "nobody:nobody"
    volumes:
      - ./monitoring/alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml:ro
      - alertmanager_data:/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://localhost:9093'
    ports:
      - "9093:9093"
    networks:
      - monitoring
    restart: unless-stopped
    mem_limit: 256m
    cpus: 0.25

  redis-exporter:
    image: oliver006/redis_exporter:v1.55.0
    container_name: redis-exporter
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    environment:
      - REDIS_ADDR=redis://redis:6379
      - REDIS_USER=monitoring
      - REDIS_PASSWORD_FILE=/run/secrets/redis_password
    secrets:
      - redis_password
    ports:
      - "9121:9121"
    networks:
      - backend
      - monitoring
    depends_on:
      - redis
    restart: unless-stopped
    mem_limit: 128m
    cpus: 0.1

  node-exporter:
    image: prom/node-exporter:v1.6.1
    container_name: node-exporter
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - SYS_TIME
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    ports:
      - "9100:9100"
    networks:
      - monitoring
    restart: unless-stopped
    mem_limit: 128m
    cpus: 0.1

  cadvisor:
    image: gcr.io/cadvisor/cadvisor:v0.47.2
    container_name: cadvisor
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - SYS_ADMIN
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    ports:
      - "8080:8080"
    networks:
      - monitoring
    restart: unless-stopped
    mem_limit: 256m
    cpus: 0.2

  loki:
    image: grafana/loki:2.9.0
    container_name: loki
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETGID
      - SETUID
    user: "10001:10001"
    volumes:
      - loki_data:/loki
      - ./monitoring/loki/loki-config.yml:/etc/loki/local-config.yaml:ro
    command: -config.file=/etc/loki/local-config.yaml
    ports:
      - "3100:3100"
    networks:
      - monitoring
    restart: unless-stopped
    mem_limit: 512m
    cpus: 0.3

  promtail:
    image: grafana/promtail:2.9.0
    container_name: promtail
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    volumes:
      - log_volume:/var/log/heibooky:ro
      - ./monitoring/promtail/promtail-config.yml:/etc/promtail/config.yml:ro
    command: -config.file=/etc/promtail/config.yml
    networks:
      - monitoring
    depends_on:
      - loki
    restart: unless-stopped
    mem_limit: 128m
    cpus: 0.1

secrets:
  redis_password:
    file: ./secrets/redis_password.txt

volumes:
  redis_data:
  static_volume:
  media_volume:
  log_volume:
  nginx_cache:
  nginx_run:
  prometheus_data:
  grafana_data:
  alertmanager_data:
  loki_data:

networks:
  backend:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
  monitoring:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
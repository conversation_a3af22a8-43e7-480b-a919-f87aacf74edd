[server]
http_port = 3000
domain = grafana.heibooky.com
root_url = %(protocol)s://%(domain)s:%(http_port)s/

[security]
admin_user = admin
admin_password = ${GF_SECURITY_ADMIN_PASSWORD}
secret_key = ${GF_SECURITY_SECRET_KEY}

[users]
allow_sign_up = false
allow_org_create = false
default_role = Viewer

[auth]
disable_login_form = false

[auth.anonymous]
enabled = false

[dashboards]
default_home_dashboard_path = /etc/grafana/provisioning/dashboards/heibooky-overview.json

[alerting]
enabled = true

[unified_alerting]
enabled = true

[log]
mode = console file
level = info

[paths]
data = /var/lib/grafana
logs = /var/log/grafana
plugins = /var/lib/grafana/plugins
provisioning = /etc/grafana/provisioning

[analytics]
reporting_enabled = false
check_for_updates = false

[feature_toggles]
enable = ngalert

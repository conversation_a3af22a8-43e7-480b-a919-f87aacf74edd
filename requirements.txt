aiohappyeyeballs==2.4.6
aiohttp==3.11.13
aiosignal==1.3.2
amqp==5.2.0
annotated-types==0.7.0
arabic-reshaper==3.0.0
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
asgiref==3.8.1
asn1crypto==1.5.1
attrs==24.3.0
autobahn==24.4.2
Automat==24.8.1
babel==2.16.0
billiard==4.2.0
blis==1.2.0
boto3==1.35.34
botocore==1.35.34
Brotli==1.1.0
cachetools==5.5.2
catalogue==2.0.10
celery==5.4.0
certifi==2024.7.4
cffi==1.17.1
channels==4.2.0
channels_redis==4.2.1
chardet==5.2.0
charset-normalizer==3.3.2
click==8.1.7
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
cloudpathlib==0.20.0
colorama==0.4.6
confection==0.1.5
constantly==23.10.4
cron-descriptor==1.4.5
cryptography==44.0.0
cssselect2==0.8.0
cymem==2.0.11
daphne==4.1.2
defusedxml==0.7.1
dj-rest-auth==7.0.1
Django==5.1.2
django-admin-honeypot==1.1.0
django-allauth==65.3.1
django-anymail==13.0
django-axes==7.0.2
django-celery-beat==2.7.0
django-cors-headers==4.6.0
django-countries==7.6.1
django-csp==4.0
django-defender==0.9.8
django-environ==0.11.2
django-formtools==2.5.1
django-honeypot==1.2.1
django-ipware==7.0.1
django-mailgun-provider==0.2.3
django-otp==1.6.0
django-phonenumber-field==8.0.0
django-prometheus==2.4.1
django-ratelimit==4.1.0
django-redis==5.4.0
django-secure==1.0.2
django-session-timeout==0.1.0
django-storages==1.14.4
django-timezone-field==7.0
django-two-factor-auth==1.17.0
djangorestframework==3.15.2
djangorestframework-simplejwt==5.3.1
easyocr==1.7.2
et_xmlfile==2.0.0
filelock==3.17.0
fonttools==4.56.0
frozenlist==1.5.0
fsspec==2025.2.0
google-auth==2.39.0
greenlet==3.1.1
gunicorn==23.0.0
html5lib==1.1
hyperlink==21.0.0
idna==3.8
imageio==2.37.0
incremental==24.7.2
iniconfig==2.0.0
Jinja2==3.1.5
jmespath==1.0.1
joblib==1.4.2
kombu==5.4.0
langcodes==3.5.0
language_data==1.3.0
lazy_loader==0.4
lxml==5.3.1
Mako==1.3.5
marisa-trie==1.2.1
markdown-it-py==3.0.0
MarkupSafe==3.0.1
mathparse==0.1.2
maxminddb==2.6.3
mdurl==0.1.2
mpmath==1.3.0
msgpack==1.1.0
multidict==6.1.0
murmurhash==1.0.12
networkx==3.4.2
ninja==********
numpy==2.1.0
opencv-python-headless==*********
openpyxl==3.1.5
oscrypto==1.3.0
packaging==24.1
pandas==2.2.2
parse==1.20.2
parse_type==0.6.4
phonenumbers==8.13.44
pillow==10.4.0
pluggy==1.5.0
preshed==3.0.9
prometheus_client==0.22.1
prompt_toolkit==3.0.47
propcache==0.3.0
psutil==7.0.0
psycopg2-binary==2.9.9
pyasn1==0.6.1
pyasn1_modules==0.4.1
pyclipper==1.3.0.post6
pycountry==24.6.1
pycparser==2.22
pydantic==2.10.6
pydantic_core==2.27.2
pydyf==0.11.0
Pygments==2.19.1
pyHanko==0.26.0
pyhanko-certvalidator==0.26.8
PyJWT==2.9.0
pyOpenSSL==24.3.0
pypdf==5.4.0
pyphen==0.17.2
pypng==0.20220715.0
pytest==8.3.3
pytest-bdd==7.3.0
pytest-django==4.9.0
python-bidi==0.6.3
python-crontab==3.2.0
python-dateutil==2.9.0.post0
python-http-client==3.3.7
python-ipware==3.0.0
python-slugify==8.0.4
pytz==2024.1
PyYAML==6.0.2
qrcode==7.4.2
redis==5.0.8
reportlab==4.3.1
requests==2.32.3
rich==13.9.4
rsa==4.9
s3transfer==0.10.2
scikit-image==0.25.1
scikit-learn==1.6.1
scipy==1.15.1
service-identity==24.2.0
setuptools==75.6.0
shapely==2.0.7
shellingham==1.5.4
six==1.16.0
smart-open==7.1.0
spacy==3.8.4
spacy-legacy==3.0.12
spacy-loggers==1.0.5
SQLAlchemy==2.0.38
sqlparse==0.5.1
srsly==2.5.1
starkbank-ecdsa==2.2.0
stripe==10.8.0
svglib==1.5.1
sympy==1.13.1
tenacity==9.0.0
text-unidecode==1.3
thinc==8.3.4
threadpoolctl==3.5.0
tifffile==2025.1.10
tinycss2==1.4.0
tinyhtml5==2.0.0
torch==2.6.0
torchvision==0.21.0
tqdm==4.67.1
Twisted==24.11.0
txaio==23.1.1
typer==0.15.1
typing_extensions==4.12.2
tzdata==2024.1
tzlocal==5.3.1
ua-parser==1.0.1
ua-parser-builtins==0.18.0.post1
uritools==4.0.3
urllib3==2.2.2
user-agents==2.2.0
vine==5.1.0
wasabi==1.1.3
wcwidth==0.2.13
weasel==0.4.1
webencodings==0.5.1
websockets==15.0.1
whitenoise==6.7.0
wrapt==1.17.2
xhtml2pdf==0.2.17
yarl==1.18.3
zope.interface==7.2

{"dashboard": {"id": null, "title": "Heibooky Application Overview", "tags": ["he<PERSON><PERSON>y", "django", "monitoring"], "style": "dark", "timezone": "browser", "panels": [{"id": 1, "title": "HTTP Request Rate", "type": "stat", "targets": [{"expr": "sum(rate(django_http_requests_total[5m]))", "legendFormat": "Requests/sec"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 50}]}}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "Error Rate", "type": "stat", "targets": [{"expr": "sum(rate(django_http_requests_total{status=~\"5..\"}[5m])) / sum(rate(django_http_requests_total[5m])) * 100", "legendFormat": "Error %"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}, "unit": "percent"}}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}}, {"id": 3, "title": "Response Time (P95)", "type": "stat", "targets": [{"expr": "histogram_quantile(0.95, sum(rate(django_http_request_duration_seconds_bucket[5m])) by (le))", "legendFormat": "P95 Latency"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 3}]}, "unit": "s"}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}}, {"id": 4, "title": "Active Requests", "type": "stat", "targets": [{"expr": "django_http_requests_active", "legendFormat": "Active"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 50}]}}}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}}, {"id": 5, "title": "HTTP Request Rate Over Time", "type": "graph", "targets": [{"expr": "sum(rate(django_http_requests_total[5m])) by (status)", "legendFormat": "{{status}}"}], "yAxes": [{"label": "Requests/sec", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 6, "title": "Response Time Distribution", "type": "graph", "targets": [{"expr": "histogram_quantile(0.50, sum(rate(django_http_request_duration_seconds_bucket[5m])) by (le))", "legendFormat": "P50"}, {"expr": "histogram_quantile(0.95, sum(rate(django_http_request_duration_seconds_bucket[5m])) by (le))", "legendFormat": "P95"}, {"expr": "histogram_quantile(0.99, sum(rate(django_http_request_duration_seconds_bucket[5m])) by (le))", "legendFormat": "P99"}], "yAxes": [{"label": "Seconds", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 7, "title": "Memory Usage", "type": "graph", "targets": [{"expr": "django_process_memory_usage_bytes / 1024 / 1024", "legendFormat": "Memory (MB)"}], "yAxes": [{"label": "MB", "min": 0}], "gridPos": {"h": 8, "w": 8, "x": 0, "y": 16}}, {"id": 8, "title": "CPU Usage", "type": "graph", "targets": [{"expr": "django_process_cpu_percent", "legendFormat": "CPU %"}], "yAxes": [{"label": "Percent", "min": 0, "max": 100}], "gridPos": {"h": 8, "w": 8, "x": 8, "y": 16}}, {"id": 9, "title": "Celery Workers", "type": "stat", "targets": [{"expr": "celery_workers_total", "legendFormat": "Workers"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 1}, {"color": "green", "value": 2}]}}}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 16}}, {"id": 10, "title": "Redis Memory Usage", "type": "graph", "targets": [{"expr": "redis_memory_used_bytes / 1024 / 1024", "legendFormat": "Used (MB)"}, {"expr": "redis_config_maxmemory_bytes / 1024 / 1024", "legendFormat": "<PERSON> (MB)"}], "yAxes": [{"label": "MB", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}}, {"id": 11, "title": "Redis Connected Clients", "type": "graph", "targets": [{"expr": "redis_connected_clients", "legendFormat": "Clients"}], "yAxes": [{"label": "Count", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "30s", "schemaVersion": 30}}
# Alert Rules for Heibooky Application
groups:
  - name: django_alerts
    rules:
      - alert: DjangoAppDown
        expr: up{job="django-app"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Django application is down"
          description: "Django application has been down for more than 1 minute"

      - alert: HighRequestLatency
        expr: django_http_request_duration_seconds{quantile="0.95"} > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High request latency detected"
          description: "95th percentile latency is above 2 seconds for 5 minutes"

      - alert: HighErrorRate
        expr: rate(django_http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is above 10% for 2 minutes"

      - alert: HighMemoryUsage
        expr: django_process_memory_usage_bytes / 1024 / 1024 / 1024 > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Application memory usage is above 1GB"

  - name: redis_alerts
    rules:
      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 30s
        labels:
          severity: critical
        annotations:
          summary: "Redis is down"
          description: "Redis has been down for more than 30 seconds"

      - alert: RedisHighMemoryUsage
        expr: redis_memory_used_bytes / redis_config_maxmemory_bytes > 0.9
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Redis high memory usage"
          description: "Redis memory usage is above 90%"

      - alert: RedisConnectionSpike
        expr: rate(redis_connected_clients[5m]) > 100
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "Redis connection spike"
          description: "Sudden increase in Redis connections"

  - name: celery_alerts
    rules:
      - alert: CeleryWorkerDown
        expr: celery_workers_total == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "No Celery workers available"
          description: "All Celery workers are down"

      - alert: CeleryQueueBacklog
        expr: celery_queue_length > 100
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Celery queue backlog"
          description: "Celery queue has more than 100 pending tasks"

      - alert: CeleryTaskFailureRate
        expr: rate(celery_task_failures_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High Celery task failure rate"
          description: "Celery task failure rate is above 10%"

  - name: system_alerts
    rules:
      - alert: HighCPUUsage
        expr: 100 - (avg by (instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is above 80% for 5 minutes"

      - alert: HighDiskUsage
        expr: (node_filesystem_size_bytes - node_filesystem_free_bytes) / node_filesystem_size_bytes > 0.85
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High disk usage"
          description: "Disk usage is above 85%"

      - alert: LowDiskSpace
        expr: node_filesystem_free_bytes / 1024 / 1024 / 1024 < 5
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Low disk space"
          description: "Less than 5GB of disk space remaining"

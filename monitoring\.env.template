# Monitoring Stack Environment Variables Template
# Copy this file to .env and fill in the actual values
# NEVER commit the actual .env file to version control

# =============================================================================
# GRAFANA CONFIGURATION
# =============================================================================

# Admin credentials (REQUIRED)
GF_SECURITY_ADMIN_PASSWORD=your_secure_admin_password_here
GF_SECURITY_SECRET_KEY=your_secret_key_here_min_32_chars

# Database configuration (optional - uses SQLite by default)
GF_DATABASE_TYPE=sqlite3
GF_DATABASE_HOST=
GF_DATABASE_NAME=grafana
GF_DATABASE_USER=
GF_DATABASE_PASSWORD=
GF_DATABASE_SSL_MODE=disable

# Server configuration
GF_SERVER_DOMAIN=grafana.heibooky.com
GF_SERVER_ROOT_URL=https://grafana.heibooky.com
GF_SERVER_PROTOCOL=https
GF_SERVER_CERT_FILE=/etc/ssl/certs/grafana.crt
GF_SERVER_CERT_KEY=/etc/ssl/private/grafana.key

# Security settings
GF_SECURITY_COOKIE_SECURE=true
GF_SECURITY_COOKIE_SAMESITE=strict
GF_SECURITY_STRICT_TRANSPORT_SECURITY=true
GF_SECURITY_LOGIN_REMEMBER_DAYS=7
GF_SECURITY_LOGIN_MAXIMUM_INACTIVE_LIFETIME_DURATION=7d
GF_SECURITY_LOGIN_MAXIMUM_LIFETIME_DURATION=30d
GF_SECURITY_PASSWORD_MIN_LENGTH=12

# User management
GF_USERS_ALLOW_SIGN_UP=false
GF_USERS_ALLOW_ORG_CREATE=false
GF_USERS_DEFAULT_ROLE=Viewer
GF_USERS_AUTO_ASSIGN_ORG=true
GF_USERS_AUTO_ASSIGN_ORG_ROLE=Viewer

# Authentication (OAuth/LDAP - configure as needed)
GF_AUTH_DISABLE_LOGIN_FORM=false
GF_AUTH_OAUTH_AUTO_LOGIN=false

# Anonymous access
GF_AUTH_ANONYMOUS_ENABLED=false

# Logging
GF_LOG_MODE=console file
GF_LOG_LEVEL=info

# Analytics
GF_ANALYTICS_REPORTING_ENABLED=false
GF_ANALYTICS_CHECK_FOR_UPDATES=false

# Performance optimization
GF_DATABASE_WAL=true
GF_DATABASE_CACHE_MODE=shared
GF_DATABASE_MAX_IDLE_CONN=10
GF_DATABASE_MAX_OPEN_CONN=100
GF_DATABASE_CONN_MAX_LIFETIME=14400
GF_DATABASE_QUERY_RETRIES=3
GF_DATABASE_QUERY_TIMEOUT=30s

# Caching configuration
GF_CACHING_ENABLED=true

# Data proxy performance
GF_DATAPROXY_TIMEOUT=30
GF_DATAPROXY_DIAL_TIMEOUT=10
GF_DATAPROXY_KEEP_ALIVE_SECONDS=30
GF_DATAPROXY_MAX_IDLE_CONNECTIONS=100
GF_DATAPROXY_MAX_IDLE_CONNECTIONS_PER_HOST=10
GF_DATAPROXY_IDLE_CONN_TIMEOUT=90
GF_DATAPROXY_TLS_HANDSHAKE_TIMEOUT=10
GF_DATAPROXY_EXPECT_CONTINUE_TIMEOUT=1

# Rendering performance
GF_RENDERING_CONCURRENT_RENDER_REQUEST_LIMIT=30
GF_RENDERING_RENDERING_TIMEOUT=20s

# Alerting performance
GF_ALERTING_CONCURRENT_RENDER_LIMIT=5
GF_ALERTING_EVALUATION_TIMEOUT_SECONDS=30
GF_ALERTING_NOTIFICATION_TIMEOUT_SECONDS=30
GF_ALERTING_MAX_ATTEMPTS=3

# Unified alerting performance
GF_UNIFIED_ALERTING_ENABLED=true
GF_UNIFIED_ALERTING_MAX_ATTEMPTS=3
GF_UNIFIED_ALERTING_MIN_INTERVAL=10s

# Live features
GF_LIVE_MAX_CONNECTIONS=100

# Metrics
GF_METRICS_ENABLED=true
GF_METRICS_INTERVAL_SECONDS=10

# Feature toggles
GF_FEATURE_TOGGLES_ENABLE=ngalert,publicDashboards

# Resource limits
GRAFANA_MEMORY_LIMIT=1G
GRAFANA_CPU_LIMIT=1.0
GRAFANA_MEMORY_REQUEST=512M
GRAFANA_CPU_REQUEST=0.25

# Data paths
GRAFANA_DATA_PATH=./monitoring/data/grafana

# =============================================================================
# SMTP CONFIGURATION (for alerts)
# =============================================================================

# SMTP server settings (REQUIRED for email alerts)
SMTP_HOST=smtp.your-provider.com
SMTP_PORT=587
SMTP_USERNAME=your_smtp_username
SMTP_PASSWORD=your_smtp_password
SMTP_FROM=<EMAIL>

# Email recipients for different alert types
ALERT_EMAIL_CRITICAL=<EMAIL>,<EMAIL>
ALERT_EMAIL_SECURITY=<EMAIL>,<EMAIL>
ALERT_EMAIL_WARNING=<EMAIL>
ALERT_EMAIL_INFO=<EMAIL>

# =============================================================================
# PROMETHEUS CONFIGURATION
# =============================================================================

# Storage retention and performance
PROMETHEUS_RETENTION_TIME=15d
PROMETHEUS_RETENTION_SIZE=10GB
PROMETHEUS_WAL_COMPRESSION=true
PROMETHEUS_MIN_BLOCK_DURATION=2h
PROMETHEUS_MAX_BLOCK_DURATION=36h

# Query settings and performance
PROMETHEUS_QUERY_TIMEOUT=2m
PROMETHEUS_QUERY_MAX_CONCURRENCY=20
PROMETHEUS_QUERY_MAX_SAMPLES=50000000
PROMETHEUS_QUERY_LOOKBACK_DELTA=5m

# Web configuration
PROMETHEUS_WEB_ENABLE_LIFECYCLE=false
PROMETHEUS_WEB_ENABLE_ADMIN_API=false
PROMETHEUS_WEB_MAX_CONNECTIONS=512

# Resource limits
PROMETHEUS_MEMORY_LIMIT=2G
PROMETHEUS_CPU_LIMIT=2.0
PROMETHEUS_MEMORY_REQUEST=1G
PROMETHEUS_CPU_REQUEST=0.5

# Data paths for performance optimization
PROMETHEUS_DATA_PATH=./monitoring/data/prometheus

# =============================================================================
# ALERTMANAGER CONFIGURATION
# =============================================================================

# Web configuration
ALERTMANAGER_WEB_EXTERNAL_URL=http://alertmanager:9093
ALERTMANAGER_WEB_ROUTE_PREFIX=/

# Cluster configuration (for HA setup)
ALERTMANAGER_CLUSTER_LISTEN_ADDRESS=0.0.0.0:9094
ALERTMANAGER_CLUSTER_PEER=

# Performance optimization
AM_CLUSTER_PEER_TIMEOUT=15s
AM_CLUSTER_GOSSIP_INTERVAL=200ms
AM_CLUSTER_PUSH_PULL_INTERVAL=60s
AM_DATA_RETENTION=120h

# Resource limits
ALERTMANAGER_MEMORY_LIMIT=512M
ALERTMANAGER_CPU_LIMIT=0.5
ALERTMANAGER_MEMORY_REQUEST=256M
ALERTMANAGER_CPU_REQUEST=0.1

# Data paths
ALERTMANAGER_DATA_PATH=./monitoring/data/alertmanager

# =============================================================================
# LOKI CONFIGURATION
# =============================================================================

# Server settings
LOKI_HTTP_LISTEN_PORT=3100
LOKI_GRPC_LISTEN_PORT=9096

# Storage settings
LOKI_STORAGE_TYPE=filesystem
LOKI_STORAGE_FILESYSTEM_DIRECTORY=/loki/chunks

# Retention settings
LOKI_RETENTION_ENABLED=true
LOKI_RETENTION_PERIOD=336h  # 14 days

# Limits and performance optimization
LOKI_INGESTION_RATE_MB=4
LOKI_INGESTION_BURST_SIZE_MB=6
LOKI_MAX_QUERY_PARALLELISM=32
LOKI_QUERY_TIMEOUT=1m
LOKI_MAX_ENTRIES_LIMIT=5000
LOKI_SPLIT_QUERIES_BY_INTERVAL=30m

# Caching configuration
LOKI_CACHE_RESULTS=true
LOKI_RESULTS_CACHE_MAX_SIZE_MB=500
LOKI_RESULTS_CACHE_TTL=1h

# Chunk configuration
LOKI_CHUNK_IDLE_PERIOD=1h
LOKI_MAX_CHUNK_AGE=1h
LOKI_CHUNK_TARGET_SIZE=1048576

# Compaction settings
LOKI_COMPACTION_INTERVAL=10m

# Resource limits
LOKI_MEMORY_LIMIT=2G
LOKI_CPU_LIMIT=1.5
LOKI_MEMORY_REQUEST=1G
LOKI_CPU_REQUEST=0.5

# Data paths
LOKI_DATA_PATH=./monitoring/data/loki

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================

# Redis password (if using Redis for caching/sessions)
REDIS_PASSWORD=your_redis_password_here

# Redis connection settings
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0

# Exporter resource limits
REDIS_EXPORTER_MEMORY_LIMIT=128M
REDIS_EXPORTER_CPU_LIMIT=0.2
REDIS_EXPORTER_MEMORY_REQUEST=64M
REDIS_EXPORTER_CPU_REQUEST=0.05

NODE_EXPORTER_MEMORY_LIMIT=128M
NODE_EXPORTER_CPU_LIMIT=0.2
NODE_EXPORTER_MEMORY_REQUEST=64M
NODE_EXPORTER_CPU_REQUEST=0.05

CADVISOR_MEMORY_LIMIT=256M
CADVISOR_CPU_LIMIT=0.3
CADVISOR_MEMORY_REQUEST=128M
CADVISOR_CPU_REQUEST=0.1

PROMTAIL_MEMORY_LIMIT=128M
PROMTAIL_CPU_LIMIT=0.2
PROMTAIL_MEMORY_REQUEST=64M
PROMTAIL_CPU_REQUEST=0.05

# =============================================================================
# POSTGRESQL CONFIGURATION (if using external DB)
# =============================================================================

# PostgreSQL connection for Grafana (optional)
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=grafana
POSTGRES_USER=grafana
POSTGRES_PASSWORD=your_postgres_password_here

# =============================================================================
# SSL/TLS CONFIGURATION
# =============================================================================

# SSL certificate paths (if using custom certificates)
SSL_CERT_PATH=/etc/ssl/certs
SSL_KEY_PATH=/etc/ssl/private

# Certificate files
GRAFANA_SSL_CERT=grafana.crt
GRAFANA_SSL_KEY=grafana.key

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================

# Backup storage settings
BACKUP_STORAGE_TYPE=local  # local, s3, gcs, azure
BACKUP_RETENTION_DAYS=30
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM

# S3 backup settings (if using S3)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_DEFAULT_REGION=us-east-1
S3_BACKUP_BUCKET=heibooky-monitoring-backups

# =============================================================================
# MONITORING AND ALERTING
# =============================================================================

# Webhook URLs for external integrations
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
TEAMS_WEBHOOK_URL=https://your-org.webhook.office.com/webhookb2/YOUR/TEAMS/WEBHOOK
PAGERDUTY_INTEGRATION_KEY=your_pagerduty_integration_key

# External monitoring endpoints
EXTERNAL_HEALTH_CHECK_URL=https://your-monitoring-service.com/webhook
UPTIME_MONITOR_URL=https://your-uptime-service.com/webhook

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Security scanning
SECURITY_SCAN_ENABLED=true
VULNERABILITY_SCAN_SCHEDULE=0 3 * * *  # Daily at 3 AM

# Rate limiting
RATE_LIMIT_ENABLED=true
MAX_REQUESTS_PER_MINUTE=100
MAX_LOGIN_ATTEMPTS=5

# IP whitelisting (comma-separated list)
ALLOWED_IPS=10.0.0.0/8,**********/12,***********/16

# =============================================================================
# DEVELOPMENT/TESTING CONFIGURATION
# =============================================================================

# Environment type
ENVIRONMENT=production  # development, staging, production

# Debug settings (NEVER enable in production)
DEBUG_MODE=false
ENABLE_PROFILING=false
LOG_QUERIES=false

# Test data settings
GENERATE_TEST_DATA=false
TEST_DATA_INTERVAL=30s

# =============================================================================
# RESOURCE LIMITS
# =============================================================================

# Memory limits (in MB)
GRAFANA_MEMORY_LIMIT=512
PROMETHEUS_MEMORY_LIMIT=2048
ALERTMANAGER_MEMORY_LIMIT=256
LOKI_MEMORY_LIMIT=1024

# CPU limits (in cores)
GRAFANA_CPU_LIMIT=1.0
PROMETHEUS_CPU_LIMIT=2.0
ALERTMANAGER_CPU_LIMIT=0.5
LOKI_CPU_LIMIT=1.0

# =============================================================================
# NETWORK CONFIGURATION
# =============================================================================

# Network settings
MONITORING_NETWORK_SUBNET=172.21.0.0/16
BACKEND_NETWORK_SUBNET=172.20.0.0/16

# Port mappings (for external access)
GRAFANA_EXTERNAL_PORT=3000
PROMETHEUS_EXTERNAL_PORT=9090
ALERTMANAGER_EXTERNAL_PORT=9093

# =============================================================================
# CUSTOM CONFIGURATION
# =============================================================================

# Custom dashboard URLs
CUSTOM_DASHBOARD_REPO=https://github.com/your-org/grafana-dashboards.git
CUSTOM_ALERT_RULES_REPO=https://github.com/your-org/prometheus-rules.git

# Integration settings
ENABLE_CUSTOM_METRICS=true
CUSTOM_METRICS_ENDPOINT=http://web:8000/monitoring/custom-metrics/

# Feature flags
ENABLE_EXPERIMENTAL_FEATURES=false
ENABLE_BETA_FEATURES=false

# Recording Rules for Performance Optimization
# These rules pre-compute frequently used queries to improve dashboard performance

groups:
  - name: django_recording_rules
    interval: 30s
    rules:
      # HTTP Request Rate (requests per second)
      - record: django:http_requests_per_second
        expr: rate(django_http_requests_total[5m])
        labels:
          job: django-app

      # HTTP Request Rate by Status Code
      - record: django:http_requests_per_second_by_status
        expr: rate(django_http_requests_total[5m])
        labels:
          job: django-app

      # HTTP Error Rate (percentage)
      - record: django:http_error_rate
        expr: |
          (
            rate(django_http_requests_total{status=~"4..|5.."}[5m]) /
            rate(django_http_requests_total[5m])
          ) * 100
        labels:
          job: django-app

      # HTTP 95th Percentile Latency
      - record: django:http_request_duration_95th_percentile
        expr: histogram_quantile(0.95, rate(django_http_request_duration_seconds_bucket[5m]))
        labels:
          job: django-app

      # HTTP 99th Percentile Latency
      - record: django:http_request_duration_99th_percentile
        expr: histogram_quantile(0.99, rate(django_http_request_duration_seconds_bucket[5m]))
        labels:
          job: django-app

      # Average Request Duration
      - record: django:http_request_duration_average
        expr: |
          rate(django_http_request_duration_seconds_sum[5m]) /
          rate(django_http_request_duration_seconds_count[5m])
        labels:
          job: django-app

      # Active Requests
      - record: django:http_requests_active
        expr: django_http_requests_active
        labels:
          job: django-app

  - name: system_recording_rules
    interval: 30s
    rules:
      # CPU Usage Percentage
      - record: node:cpu_usage_percent
        expr: |
          100 - (
            avg by (instance) (
              rate(node_cpu_seconds_total{mode="idle"}[5m])
            ) * 100
          )

      # Memory Usage Percentage
      - record: node:memory_usage_percent
        expr: |
          (
            (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) /
            node_memory_MemTotal_bytes
          ) * 100

      # Disk Usage Percentage
      - record: node:disk_usage_percent
        expr: |
          (
            (node_filesystem_size_bytes - node_filesystem_free_bytes) /
            node_filesystem_size_bytes
          ) * 100

      # Network I/O Rate
      - record: node:network_io_bytes_per_second
        expr: |
          rate(node_network_receive_bytes_total[5m]) +
          rate(node_network_transmit_bytes_total[5m])

      # Disk I/O Rate
      - record: node:disk_io_bytes_per_second
        expr: |
          rate(node_disk_read_bytes_total[5m]) +
          rate(node_disk_written_bytes_total[5m])

  - name: redis_recording_rules
    interval: 30s
    rules:
      # Redis Memory Usage Percentage
      - record: redis:memory_usage_percent
        expr: |
          (redis_memory_used_bytes / redis_config_maxmemory_bytes) * 100

      # Redis Connection Rate
      - record: redis:connections_per_second
        expr: rate(redis_connected_clients[5m])

      # Redis Command Rate
      - record: redis:commands_per_second
        expr: rate(redis_commands_processed_total[5m])

      # Redis Hit Rate
      - record: redis:hit_rate_percent
        expr: |
          (
            rate(redis_keyspace_hits_total[5m]) /
            (rate(redis_keyspace_hits_total[5m]) + rate(redis_keyspace_misses_total[5m]))
          ) * 100

  - name: celery_recording_rules
    interval: 30s
    rules:
      # Celery Task Rate
      - record: celery:tasks_per_second
        expr: rate(celery_tasks_total[5m])

      # Celery Task Failure Rate
      - record: celery:task_failure_rate_percent
        expr: |
          (
            rate(celery_tasks_total{state="FAILURE"}[5m]) /
            rate(celery_tasks_total[5m])
          ) * 100

      # Celery Queue Length
      - record: celery:queue_length
        expr: celery_queue_length

      # Celery Worker Count
      - record: celery:workers_active
        expr: celery_workers_total

  - name: application_health_recording_rules
    interval: 60s
    rules:
      # Application Availability
      - record: app:availability_percent
        expr: |
          (
            avg_over_time(up{job="django-app"}[5m])
          ) * 100

      # Database Availability
      - record: app:database_availability_percent
        expr: |
          (
            avg_over_time(up{job="postgres"}[5m])
          ) * 100

      # Cache Availability
      - record: app:cache_availability_percent
        expr: |
          (
            avg_over_time(up{job="redis"}[5m])
          ) * 100

      # Overall System Health Score
      - record: app:health_score
        expr: |
          (
            app:availability_percent +
            app:database_availability_percent +
            app:cache_availability_percent
          ) / 3

  - name: security_recording_rules
    interval: 60s
    rules:
      # Failed Login Attempts Rate
      - record: security:failed_login_rate
        expr: rate(django_http_requests_total{status="401"}[5m])

      # Suspicious Request Rate (4xx errors)
      - record: security:suspicious_request_rate
        expr: rate(django_http_requests_total{status=~"4.."}[5m])

      # High Error Rate Indicator
      - record: security:high_error_rate_indicator
        expr: |
          (
            rate(django_http_requests_total{status=~"5.."}[5m]) /
            rate(django_http_requests_total[5m])
          ) > 0.05

  - name: performance_sli_recording_rules
    interval: 30s
    rules:
      # Availability SLI (99.9% target)
      - record: sli:availability
        expr: |
          (
            rate(django_http_requests_total{status!~"5.."}[5m]) /
            rate(django_http_requests_total[5m])
          )

      # Latency SLI (95% of requests under 500ms)
      - record: sli:latency
        expr: |
          (
            histogram_quantile(0.95, rate(django_http_request_duration_seconds_bucket[5m])) < 0.5
          )

      # Error Rate SLI (less than 1% error rate)
      - record: sli:error_rate
        expr: |
          (
            rate(django_http_requests_total{status=~"5.."}[5m]) /
            rate(django_http_requests_total[5m])
          ) < 0.01

      # Throughput SLI (requests per second)
      - record: sli:throughput
        expr: rate(django_http_requests_total[5m])

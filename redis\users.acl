# Redis ACL Configuration
# Default user - disabled for security
user default off

# Django application user
user django_app on >3SWADHHP31zQ2NzhftByOKuss ~* &* +@all -@dangerous -flushdb -flushall -shutdown -debug -eval -script -config -keys

# Admin user for maintenance (use strong password)
user admin on >0sZ2hCqoukDTx01q76fbKqtfU ~* &* +@all

# Read-only user for monitoring
user monitoring on >vyFF32lMZ3XWIK9c8KT2bB7Gb ~monitor:* ~stats:* &* +@read +ping +info +client

# Celery worker user (specific permissions for task processing)
user celery_worker on >UHXtOVZx0ln9GKU2PglsAwEhB ~* &* +@all -@dangerous -flushdb -flushall -shutdown -debug -eval -script -config -keys

# Celery beat user (specific permissions for scheduling tasks)
user celery_beat on >celery_beat_password ~* &* +@all -@dangerous -flushdb -flushall -shutdown -debug -eval -script -config -keys

# Session management user (limited to session operations)
user session_manager on >4YzmwctZrxpNQjkN9WFdw6IF8 ~session:* &* +@read +@write +@list +@hash +@string +ping +ttl +expire +del

# Cache user (for Django cache operations)
user cache_user on >x5NuVQBMVrbY6uAXzG2mlJ6Iy ~cache:* &* +@read +@write +@string +@hash +@list +@set +ping +ttl +expire +del

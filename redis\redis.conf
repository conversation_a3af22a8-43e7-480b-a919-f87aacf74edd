# Redis Configuration for Production
# Basic server configuration
port 6379
bind 0.0.0.0
timeout 300
tcp-keepalive 300

# Memory management
maxmemory 400mb
maxmemory-policy allkeys-lru

# Persistence configuration
save 900 1
save 300 10
save 60 10000

# Enable RDB persistence
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# Enable AOF persistence for better durability
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# Security settings
protected-mode yes

# ACL configuration
aclfile /etc/redis/users.acl

# Logging
loglevel notice
logfile ""

# Network settings
tcp-backlog 511

# Client settings
maxclients 10000

# Memory settings
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# HyperLogLog settings
hll-sparse-max-bytes 3000

# Streams settings
stream-node-max-bytes 4096
stream-node-max-entries 100

# Active rehashing
activerehashing yes

# Client output buffer limits
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# Client query buffer limit
client-query-buffer-limit 1gb

# Protocol settings
proto-max-bulk-len 512mb

# Frequency of rehashing
hz 10

# Enable dynamic hz
dynamic-hz yes

# AOF rewrite settings
aof-rewrite-incremental-fsync yes

# RDB file settings
rdb-save-incremental-fsync yes

# LFU settings
lfu-log-factor 10
lfu-decay-time 1

# Lazy freeing
lazyfree-lazy-eviction no
lazyfree-lazy-expire no
lazyfree-lazy-server-del no
replica-lazy-flush no

# Threaded I/O (Redis 6.0+)
# Note: io-threads-do-reads is disabled to prevent crashes if TLS is enabled
# due to a known Redis bug causing double-free crashes with TLS + threaded reads
# Monitor Redis updates and re-enable when the TLS threading bug is fixed
io-threads 4
io-threads-do-reads no

# Security hardening
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command KEYS ""
rename-command CONFIG "CONFIG_b73a8b4c5f6e4d2a9e1f3c7b8a5d9e2f"
rename-command DEBUG ""
rename-command EVAL ""
rename-command SHUTDOWN "SHUTDOWN_a8c4e6f2b9d1c5e7a3f8b2e9c4d6f1a7"

# Disable dangerous commands completely
rename-command SCRIPT ""

# TLS Settings (if using TLS)
# port 0
# tls-port 6380
# tls-cert-file /etc/ssl/certs/redis.crt
# tls-key-file /etc/ssl/private/redis.key
# tls-ca-cert-file /etc/ssl/certs/ca.crt

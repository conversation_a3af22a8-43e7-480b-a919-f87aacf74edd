# Prometheus Configuration
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  scrape_timeout: 10s
  query_log_file: /prometheus/query.log
  external_labels:
    cluster: 'heibooky-production'
    environment: 'production'
    region: 'us-east-1'
    replica: 'prometheus-1'

rule_files:
  - "alert_rules.yml"
  - "recording_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
      timeout: 10s
      api_version: v2

scrape_configs:
  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 15s
    scrape_timeout: 10s
    metrics_path: /metrics
    honor_labels: false
    honor_timestamps: true
    scheme: http

  # Django application metrics
  - job_name: 'django-app'
    static_configs:
      - targets: ['web:8000']
    metrics_path: '/monitoring/metrics/'
    scrape_interval: 15s
    scrape_timeout: 10s
    scheme: http
    honor_labels: false
    honor_timestamps: true
    params:
      format: ['prometheus']
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: web:8000

  # Redis metrics
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 15s
    scrape_timeout: 10s
    scheme: http
    honor_labels: false
    honor_timestamps: true

  # PostgreSQL metrics (if needed)
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s
    scrape_timeout: 10s
    scheme: http
    honor_labels: false
    honor_timestamps: true

  # Node exporter for system metrics
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s
    scrape_timeout: 10s
    scheme: http
    honor_labels: false
    honor_timestamps: true

  # Nginx metrics
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']
    scrape_interval: 15s
    scrape_timeout: 10s
    scheme: http
    honor_labels: false
    honor_timestamps: true

  # Celery metrics
  - job_name: 'celery'
    static_configs:
      - targets: ['web:8000']
    metrics_path: '/monitoring/celery-metrics/'
    scrape_interval: 30s
    scrape_timeout: 15s
    scheme: http
    honor_labels: false
    honor_timestamps: true

  # Docker container metrics
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s
    scrape_timeout: 15s
    scheme: http
    honor_labels: false
    honor_timestamps: true
    metrics_path: /metrics

  # Alertmanager metrics
  - job_name: 'alertmanager'
    static_configs:
      - targets: ['alertmanager:9093']
    scrape_interval: 15s
    scrape_timeout: 10s
    scheme: http
    honor_labels: false
    honor_timestamps: true

  # Grafana metrics
  - job_name: 'grafana'
    static_configs:
      - targets: ['grafana:3000']
    metrics_path: /metrics
    scrape_interval: 30s
    scrape_timeout: 10s
    scheme: http
    honor_labels: false
    honor_timestamps: true

  # Loki metrics
  - job_name: 'loki'
    static_configs:
      - targets: ['loki:3100']
    metrics_path: /metrics
    scrape_interval: 30s
    scrape_timeout: 10s
    scheme: http
    honor_labels: false
    honor_timestamps: true

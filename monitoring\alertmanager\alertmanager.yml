# Alertmanager Configuration
global:
  smtp_smarthost: '${SMTP_HOST}:${SMTP_PORT}'
  smtp_from: '${SMTP_FROM}'
  smtp_auth_username: '${SMTP_USERNAME}'
  smtp_auth_password: '${SMTP_PASSWORD}'
  smtp_auth_identity: '${SMTP_FROM}'
  smtp_require_tls: true
  http_config:
    tls_config:
      insecure_skip_verify: false
  resolve_timeout: 5m

templates:
  - '/etc/alertmanager/templates/*.tmpl'

route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 30s
  group_interval: 5m
  repeat_interval: 12h
  receiver: 'default-receiver'
  routes:
    # Critical alerts - immediate notification
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 10s
      group_interval: 2m
      repeat_interval: 30m
      continue: true

    # Security alerts - immediate notification
    - match:
        category: security
      receiver: 'security-alerts'
      group_wait: 10s
      group_interval: 1m
      repeat_interval: 15m
      continue: true

    # Warning alerts - less frequent
    - match:
        severity: warning
      receiver: 'warning-alerts'
      group_wait: 2m
      group_interval: 10m
      repeat_interval: 6h

    # Info alerts - daily digest
    - match:
        severity: info
      receiver: 'info-alerts'
      group_wait: 10m
      group_interval: 1h
      repeat_interval: 24h

receivers:
  - name: 'default-receiver'
    webhook_configs:
      - url: 'http://web:8000/monitoring/webhook/'
        send_resolved: true
        http_config:
          tls_config:
            insecure_skip_verify: false
        max_alerts: 0

  - name: 'critical-alerts'
    email_configs:
      - to: '${ALERT_EMAIL_CRITICAL}'
        from: '${SMTP_FROM}'
        subject: '🚨 CRITICAL ALERT: {{ .GroupLabels.alertname }} - {{ .GroupLabels.cluster }}'
        html: |
          <!DOCTYPE html>
          <html>
          <head>
              <meta charset="UTF-8">
              <title>Critical Alert</title>
              <style>
                  body { font-family: Arial, sans-serif; margin: 20px; }
                  .alert { background-color: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 10px 0; border-radius: 5px; }
                  .critical { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
                  .resolved { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
                  .details { margin-top: 10px; }
                  .timestamp { font-size: 0.9em; color: #666; }
              </style>
          </head>
          <body>
              <h2>🚨 Critical Alert Notification</h2>
              <p><strong>Cluster:</strong> {{ .GroupLabels.cluster }}</p>
              <p><strong>Environment:</strong> {{ .GroupLabels.environment }}</p>
              <p><strong>Time:</strong> {{ .CommonAnnotations.timestamp }}</p>

              {{ range .Alerts }}
              <div class="alert {{ if eq .Status "resolved" }}resolved{{ else }}critical{{ end }}">
                  <h3>{{ .Annotations.summary }}</h3>
                  <p><strong>Status:</strong> {{ .Status }}</p>
                  <p><strong>Severity:</strong> {{ .Labels.severity }}</p>
                  <p><strong>Instance:</strong> {{ .Labels.instance }}</p>
                  <div class="details">
                      <p>{{ .Annotations.description }}</p>
                      {{ if .Annotations.runbook_url }}
                      <p><a href="{{ .Annotations.runbook_url }}">📖 Runbook</a></p>
                      {{ end }}
                  </div>
                  <p class="timestamp">
                      {{ if eq .Status "firing" }}Started: {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}{{ end }}
                      {{ if eq .Status "resolved" }}Resolved: {{ .EndsAt.Format "2006-01-02 15:04:05 UTC" }}{{ end }}
                  </p>
              </div>
              {{ end }}

              <p><small>This is an automated alert from Heibooky Monitoring System</small></p>
          </body>
          </html>
        headers:
          X-Priority: '1'
          X-MSMail-Priority: 'High'
          Importance: 'high'
    webhook_configs:
      - url: 'http://web:8000/monitoring/webhook/'
        send_resolved: true
        http_config:
          tls_config:
            insecure_skip_verify: false

  - name: 'security-alerts'
    email_configs:
      - to: '${ALERT_EMAIL_SECURITY}'
        from: '${SMTP_FROM}'
        subject: '🔒 SECURITY ALERT: {{ .GroupLabels.alertname }} - {{ .GroupLabels.cluster }}'
        html: |
          <!DOCTYPE html>
          <html>
          <head>
              <meta charset="UTF-8">
              <title>Security Alert</title>
              <style>
                  body { font-family: Arial, sans-serif; margin: 20px; }
                  .alert { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0; border-radius: 5px; }
                  .security { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
                  .resolved { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
                  .details { margin-top: 10px; }
                  .timestamp { font-size: 0.9em; color: #666; }
              </style>
          </head>
          <body>
              <h2>🔒 Security Alert Notification</h2>
              <p><strong>Cluster:</strong> {{ .GroupLabels.cluster }}</p>
              <p><strong>Environment:</strong> {{ .GroupLabels.environment }}</p>
              <p><strong>Time:</strong> {{ .CommonAnnotations.timestamp }}</p>

              {{ range .Alerts }}
              <div class="alert {{ if eq .Status "resolved" }}resolved{{ else }}security{{ end }}">
                  <h3>{{ .Annotations.summary }}</h3>
                  <p><strong>Status:</strong> {{ .Status }}</p>
                  <p><strong>Category:</strong> {{ .Labels.category }}</p>
                  <p><strong>Instance:</strong> {{ .Labels.instance }}</p>
                  <div class="details">
                      <p>{{ .Annotations.description }}</p>
                      {{ if .Annotations.remediation }}
                      <p><strong>Remediation:</strong> {{ .Annotations.remediation }}</p>
                      {{ end }}
                  </div>
                  <p class="timestamp">
                      {{ if eq .Status "firing" }}Started: {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}{{ end }}
                      {{ if eq .Status "resolved" }}Resolved: {{ .EndsAt.Format "2006-01-02 15:04:05 UTC" }}{{ end }}
                  </p>
              </div>
              {{ end }}

              <p><small>This is an automated security alert from Heibooky Monitoring System</small></p>
          </body>
          </html>
        headers:
          X-Priority: '1'
          X-MSMail-Priority: 'High'
          Importance: 'high'
    webhook_configs:
      - url: 'http://web:8000/monitoring/webhook/'
        send_resolved: true

  - name: 'warning-alerts'
    email_configs:
      - to: '${ALERT_EMAIL_WARNING}'
        from: '${SMTP_FROM}'
        subject: '⚠️ WARNING: {{ .GroupLabels.alertname }} - {{ .GroupLabels.cluster }}'
        html: |
          <!DOCTYPE html>
          <html>
          <head>
              <meta charset="UTF-8">
              <title>Warning Alert</title>
              <style>
                  body { font-family: Arial, sans-serif; margin: 20px; }
                  .alert { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0; border-radius: 5px; }
                  .warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
                  .resolved { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
                  .details { margin-top: 10px; }
                  .timestamp { font-size: 0.9em; color: #666; }
              </style>
          </head>
          <body>
              <h2>⚠️ Warning Alert Notification</h2>
              <p><strong>Cluster:</strong> {{ .GroupLabels.cluster }}</p>
              <p><strong>Environment:</strong> {{ .GroupLabels.environment }}</p>

              {{ range .Alerts }}
              <div class="alert {{ if eq .Status "resolved" }}resolved{{ else }}warning{{ end }}">
                  <h3>{{ .Annotations.summary }}</h3>
                  <p><strong>Status:</strong> {{ .Status }}</p>
                  <p><strong>Severity:</strong> {{ .Labels.severity }}</p>
                  <p><strong>Instance:</strong> {{ .Labels.instance }}</p>
                  <div class="details">
                      <p>{{ .Annotations.description }}</p>
                  </div>
                  <p class="timestamp">
                      {{ if eq .Status "firing" }}Started: {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}{{ end }}
                      {{ if eq .Status "resolved" }}Resolved: {{ .EndsAt.Format "2006-01-02 15:04:05 UTC" }}{{ end }}
                  </p>
              </div>
              {{ end }}

              <p><small>This is an automated warning from Heibooky Monitoring System</small></p>
          </body>
          </html>

  - name: 'info-alerts'
    email_configs:
      - to: '${ALERT_EMAIL_INFO}'
        from: '${SMTP_FROM}'
        subject: 'ℹ️ INFO: Daily Monitoring Digest - {{ .GroupLabels.cluster }}'
        html: |
          <!DOCTYPE html>
          <html>
          <head>
              <meta charset="UTF-8">
              <title>Info Alert</title>
              <style>
                  body { font-family: Arial, sans-serif; margin: 20px; }
                  .alert { background-color: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; margin: 10px 0; border-radius: 5px; }
                  .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
                  .details { margin-top: 10px; }
                  .timestamp { font-size: 0.9em; color: #666; }
              </style>
          </head>
          <body>
              <h2>ℹ️ Information Alert</h2>
              <p><strong>Cluster:</strong> {{ .GroupLabels.cluster }}</p>
              <p><strong>Environment:</strong> {{ .GroupLabels.environment }}</p>

              {{ range .Alerts }}
              <div class="alert info">
                  <h3>{{ .Annotations.summary }}</h3>
                  <p><strong>Status:</strong> {{ .Status }}</p>
                  <p><strong>Instance:</strong> {{ .Labels.instance }}</p>
                  <div class="details">
                      <p>{{ .Annotations.description }}</p>
                  </div>
                  <p class="timestamp">Time: {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}</p>
              </div>
              {{ end }}

              <p><small>This is an automated info alert from Heibooky Monitoring System</small></p>
          </body>
          </html>

inhibit_rules:
  # Inhibit warning alerts when critical alerts are firing for the same service
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'instance', 'job']

  # Inhibit info alerts when warning or critical alerts are firing
  - source_match:
      severity: 'warning'
    target_match:
      severity: 'info'
    equal: ['alertname', 'instance', 'job']

  - source_match:
      severity: 'critical'
    target_match:
      severity: 'info'
    equal: ['alertname', 'instance', 'job']

  # Inhibit duplicate alerts from the same instance
  - source_match:
      alertname: 'InstanceDown'
    target_match_re:
      alertname: '.*'
    equal: ['instance']
